// Sample resume data - replace with your actual CV information
export const resumeData = {
    personalInfo: {
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "+****************",
        location: "San Francisco, CA",
        linkedin: "https://linkedin.com/in/johndoe",
        github: "https://github.com/johndoe",
        website: "https://johndoe.dev"
    },
    summary: "Experienced software engineer with 5+ years of expertise in full-stack development, specializing in TypeScript, React, and Node.js. Passionate about building scalable applications and leading development teams.",
    experience: [
        {
            company: "Tech Solutions Inc.",
            position: "Senior Software Engineer",
            startDate: "2022-01",
            endDate: "Present",
            description: "Lead development of enterprise web applications serving 100k+ users",
            responsibilities: [
                "Architected and implemented microservices using Node.js and TypeScript",
                "Led a team of 4 developers in agile development practices",
                "Reduced application load time by 40% through performance optimizations",
                "Mentored junior developers and conducted code reviews"
            ],
            technologies: ["TypeScript", "React", "Node.js", "PostgreSQL", "AWS", "Docker"]
        },
        {
            company: "StartupXYZ",
            position: "Full Stack Developer",
            startDate: "2020-03",
            endDate: "2021-12",
            description: "Developed and maintained web applications for a fast-growing fintech startup",
            responsibilities: [
                "Built responsive web applications using React and TypeScript",
                "Developed RESTful APIs using Express.js and MongoDB",
                "Implemented automated testing with Jest and Cypress",
                "Collaborated with design team to implement pixel-perfect UIs"
            ],
            technologies: ["JavaScript", "React", "Express.js", "MongoDB", "Jest", "Cypress"]
        },
        {
            company: "Digital Agency Co.",
            position: "Junior Developer",
            startDate: "2019-06",
            endDate: "2020-02",
            description: "Worked on various client projects ranging from e-commerce to corporate websites",
            responsibilities: [
                "Developed custom WordPress themes and plugins",
                "Created responsive websites using HTML, CSS, and JavaScript",
                "Integrated third-party APIs and payment systems",
                "Provided technical support and maintenance for client websites"
            ],
            technologies: ["HTML", "CSS", "JavaScript", "PHP", "WordPress", "MySQL"]
        }
    ],
    education: [
        {
            institution: "University of California, Berkeley",
            degree: "Bachelor of Science",
            field: "Computer Science",
            startDate: "2015-09",
            endDate: "2019-05",
            gpa: "3.7",
            honors: ["Dean's List", "Magna Cum Laude"]
        }
    ],
    skills: [
        {
            category: "Programming Languages",
            skills: ["TypeScript", "JavaScript", "Python", "Java", "PHP", "SQL"]
        },
        {
            category: "Frontend Technologies",
            skills: ["React", "Vue.js", "HTML5", "CSS3", "Sass", "Tailwind CSS"]
        },
        {
            category: "Backend Technologies",
            skills: ["Node.js", "Express.js", "Django", "Spring Boot", "GraphQL", "REST APIs"]
        },
        {
            category: "Databases",
            skills: ["PostgreSQL", "MongoDB", "MySQL", "Redis", "Elasticsearch"]
        },
        {
            category: "Cloud & DevOps",
            skills: ["AWS", "Docker", "Kubernetes", "CI/CD", "Jenkins", "GitHub Actions"]
        },
        {
            category: "Tools & Others",
            skills: ["Git", "Jira", "Figma", "Postman", "VS Code", "Linux"]
        }
    ],
    projects: [
        {
            name: "E-commerce Platform",
            description: "Full-stack e-commerce application with payment integration and admin dashboard",
            technologies: ["React", "Node.js", "PostgreSQL", "Stripe API", "AWS"],
            github: "https://github.com/johndoe/ecommerce-platform",
            url: "https://demo-ecommerce.johndoe.dev",
            startDate: "2023-01",
            endDate: "2023-06"
        },
        {
            name: "Task Management App",
            description: "Collaborative task management application with real-time updates",
            technologies: ["Vue.js", "Express.js", "Socket.io", "MongoDB"],
            github: "https://github.com/johndoe/task-manager",
            startDate: "2022-08",
            endDate: "2022-12"
        }
    ],
    certifications: [
        {
            name: "AWS Certified Solutions Architect",
            issuer: "Amazon Web Services",
            date: "2023-03",
            expiryDate: "2026-03",
            credentialId: "AWS-SAA-123456"
        },
        {
            name: "Professional Scrum Master I",
            issuer: "Scrum.org",
            date: "2022-11",
            credentialId: "PSM-789012"
        }
    ]
};
// Helper function to convert resume data to a searchable text format
export function getResumeAsText() {
    const resume = resumeData;
    let text = `Name: ${resume.personalInfo.name}\n`;
    text += `Email: ${resume.personalInfo.email}\n`;
    text += `Phone: ${resume.personalInfo.phone}\n`;
    text += `Location: ${resume.personalInfo.location}\n\n`;
    text += `SUMMARY:\n${resume.summary}\n\n`;
    text += `EXPERIENCE:\n`;
    resume.experience.forEach((exp, index) => {
        text += `${index + 1}. ${exp.position} at ${exp.company} (${exp.startDate} - ${exp.endDate})\n`;
        text += `   ${exp.description}\n`;
        text += `   Responsibilities:\n`;
        exp.responsibilities.forEach(resp => {
            text += `   - ${resp}\n`;
        });
        if (exp.technologies) {
            text += `   Technologies: ${exp.technologies.join(', ')}\n`;
        }
        text += '\n';
    });
    text += `EDUCATION:\n`;
    resume.education.forEach((edu, index) => {
        text += `${index + 1}. ${edu.degree} in ${edu.field} from ${edu.institution} (${edu.startDate} - ${edu.endDate})\n`;
        if (edu.gpa)
            text += `   GPA: ${edu.gpa}\n`;
        if (edu.honors)
            text += `   Honors: ${edu.honors.join(', ')}\n`;
        text += '\n';
    });
    text += `SKILLS:\n`;
    resume.skills.forEach(skillGroup => {
        text += `${skillGroup.category}: ${skillGroup.skills.join(', ')}\n`;
    });
    text += '\n';
    text += `PROJECTS:\n`;
    resume.projects.forEach((project, index) => {
        text += `${index + 1}. ${project.name}\n`;
        text += `   ${project.description}\n`;
        text += `   Technologies: ${project.technologies.join(', ')}\n`;
        if (project.url)
            text += `   URL: ${project.url}\n`;
        if (project.github)
            text += `   GitHub: ${project.github}\n`;
        text += '\n';
    });
    text += `CERTIFICATIONS:\n`;
    resume.certifications.forEach((cert, index) => {
        text += `${index + 1}. ${cert.name} from ${cert.issuer} (${cert.date})\n`;
        if (cert.credentialId)
            text += `   Credential ID: ${cert.credentialId}\n`;
        text += '\n';
    });
    return text;
}
//# sourceMappingURL=resume-data.js.map