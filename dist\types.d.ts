export interface PersonalInfo {
    name: string;
    email: string;
    phone: string;
    location: string;
    linkedin?: string;
    github?: string;
    website?: string;
}
export interface Experience {
    company: string;
    position: string;
    startDate: string;
    endDate: string | 'Present';
    description: string;
    responsibilities: string[];
    technologies?: string[];
}
export interface Education {
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate: string | 'Present';
    gpa?: string;
    honors?: string[];
}
export interface Skill {
    category: string;
    skills: string[];
}
export interface Project {
    name: string;
    description: string;
    technologies: string[];
    url?: string;
    github?: string;
    startDate?: string;
    endDate?: string;
}
export interface Certification {
    name: string;
    issuer: string;
    date: string;
    expiryDate?: string;
    credentialId?: string;
}
export interface Resume {
    personalInfo: PersonalInfo;
    summary: string;
    experience: Experience[];
    education: Education[];
    skills: Skill[];
    projects: Project[];
    certifications: Certification[];
}
export interface EmailRequest {
    recipient: string;
    subject: string;
    body: string;
}
//# sourceMappingURL=types.d.ts.map