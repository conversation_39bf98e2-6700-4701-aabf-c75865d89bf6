# OpenRouter API Configuration
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# SMTP Email Configuration
# For Gmail, use: smtp.gmail.com, port 587
# For Outlook, use: smtp-mail.outlook.com, port 587
# For Yahoo, use: smtp.mail.yahoo.com, port 587
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here

# Note: For Gmail, you need to use an "App Password" instead of your regular password
# 1. Enable 2-factor authentication on your Google account
# 2. Go to Google Account settings > Security > App passwords
# 3. Generate a new app password for "Mail"
# 4. Use that app password in SMTP_PASS

# For other email providers, check their SMTP settings documentation

# Example .env file - copy this to .env and fill in your actual values
