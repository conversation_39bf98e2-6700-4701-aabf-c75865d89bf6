import axios from 'axios';
import { getResumeAsText } from './resume-data.js';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
if (!OPENROUTER_API_KEY) {
    throw new Error('OPENROUTER_API_KEY environment variable is required');
}
export async function getAnswerFromResume(question) {
    try {
        const resumeText = getResumeAsText();
        const prompt = `Here's my resume:

${resumeText}

Answer this question based on the resume information above: ${question}

Please provide a clear, concise answer based only on the information provided in the resume. If the information is not available in the resume, please say so.`;
        const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
            model: 'gpt-3.5-turbo',
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 500,
            temperature: 0.7
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000', // Optional: for analytics
                'X-Title': 'CV Chat MCP Server' // Optional: for analytics
            }
        });
        if (response.data && response.data.choices && response.data.choices.length > 0) {
            return {
                success: true,
                answer: response.data.choices[0].message.content.trim()
            };
        }
        else {
            return {
                success: false,
                answer: '',
                error: 'No response received from OpenRouter API'
            };
        }
    }
    catch (error) {
        console.error('Error calling OpenRouter API:', error);
        let errorMessage = 'Failed to get answer from OpenRouter API';
        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            errorMessage = `API Error: ${error.response.status} - ${error.response.data?.error?.message || error.response.statusText}`;
        }
        else if (error.request) {
            // The request was made but no response was received
            errorMessage = 'No response received from OpenRouter API';
        }
        else {
            // Something happened in setting up the request that triggered an Error
            errorMessage = error.message;
        }
        return {
            success: false,
            answer: '',
            error: errorMessage
        };
    }
}
// Helper function to validate questions
export function isValidQuestion(question) {
    if (!question || typeof question !== 'string') {
        return false;
    }
    const trimmed = question.trim();
    return trimmed.length > 0 && trimmed.length <= 1000; // Reasonable length limit
}
// Helper function to get suggested questions
export function getSuggestedQuestions() {
    return [
        "What role did I have at my last position?",
        "What technologies do I have experience with?",
        "What is my educational background?",
        "How many years of experience do I have?",
        "What projects have I worked on?",
        "What certifications do I have?",
        "What are my key skills?",
        "Where did I work before my current job?",
        "What programming languages do I know?",
        "What is my contact information?"
    ];
}
//# sourceMappingURL=cv-chat-service.js.map