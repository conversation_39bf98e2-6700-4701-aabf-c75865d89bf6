import { EmailRequest } from "./types.js";
export interface EmailResponse {
    success: boolean;
    messageId?: string;
    error?: string;
}
export declare function sendEmail(emailRequest: EmailRequest): Promise<EmailResponse>;
export declare function testEmailConfiguration(): Promise<EmailResponse>;
export declare function getEmailConfigurationStatus(): {
    configured: boolean;
    missingVariables: string[];
};
//# sourceMappingURL=email-service.d.ts.map