import nodemailer from "nodemailer";
// Get email configuration from environment variables
function getEmailConfig() {
    const host = process.env.SMTP_HOST;
    const port = process.env.SMTP_PORT;
    const user = process.env.SMTP_USER;
    const pass = process.env.SMTP_PASS;
    if (!host || !port || !user || !pass) {
        throw new Error("Email configuration missing. Please set SMTP_HOST, SMTP_PORT, SMTP_USER, and SMTP_PASS environment variables.");
    }
    return {
        host,
        port: parseInt(port, 10),
        secure: parseInt(port, 10) === 465, // true for 465, false for other ports
        auth: {
            user,
            pass,
        },
    };
}
// Create transporter instance
let transporter = null;
function getTransporter() {
    if (!transporter) {
        const config = getEmailConfig();
        transporter = nodemailer.createTransport(config);
    }
    return transporter;
}
// Send email function
export async function sendEmail(emailRequest) {
    try {
        // Validate email request
        const validation = validateEmailRequest(emailRequest);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.error,
            };
        }
        const transporter = getTransporter();
        // Verify transporter configuration
        try {
            await transporter.verify();
        }
        catch (verifyError) {
            return {
                success: false,
                error: `SMTP configuration error: ${verifyError.message}`,
            };
        }
        // Send email
        const info = await transporter.sendMail({
            from: process.env.SMTP_USER, // sender address
            to: emailRequest.recipient, // recipient address
            subject: emailRequest.subject, // subject line
            text: emailRequest.body, // plain text body
            html: emailRequest.body.replace(/\n/g, "<br>"), // simple HTML conversion
        });
        return {
            success: true,
            messageId: info.messageId,
        };
    }
    catch (error) {
        console.error("Error sending email:", error);
        let errorMessage = "Failed to send email";
        if (error.code === "EAUTH") {
            errorMessage =
                "Email authentication failed. Please check your SMTP credentials.";
        }
        else if (error.code === "ECONNECTION") {
            errorMessage =
                "Failed to connect to SMTP server. Please check your SMTP configuration.";
        }
        else if (error.responseCode) {
            errorMessage = `SMTP Error ${error.responseCode}: ${error.response}`;
        }
        else {
            errorMessage = error.message || errorMessage;
        }
        return {
            success: false,
            error: errorMessage,
        };
    }
}
function validateEmailRequest(emailRequest) {
    if (!emailRequest.recipient || typeof emailRequest.recipient !== "string") {
        return { isValid: false, error: "Recipient email is required" };
    }
    if (!emailRequest.subject || typeof emailRequest.subject !== "string") {
        return { isValid: false, error: "Email subject is required" };
    }
    if (!emailRequest.body || typeof emailRequest.body !== "string") {
        return { isValid: false, error: "Email body is required" };
    }
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailRequest.recipient)) {
        return { isValid: false, error: "Invalid recipient email format" };
    }
    // Length validations
    if (emailRequest.subject.length > 200) {
        return {
            isValid: false,
            error: "Email subject too long (max 200 characters)",
        };
    }
    if (emailRequest.body.length > 10000) {
        return {
            isValid: false,
            error: "Email body too long (max 10,000 characters)",
        };
    }
    return { isValid: true };
}
// Test email configuration
export async function testEmailConfiguration() {
    try {
        const transporter = getTransporter();
        await transporter.verify();
        return {
            success: true,
            messageId: "configuration-test",
        };
    }
    catch (error) {
        return {
            success: false,
            error: `Email configuration test failed: ${error.message}`,
        };
    }
}
// Get email configuration status
export function getEmailConfigurationStatus() {
    const requiredVars = ["SMTP_HOST", "SMTP_PORT", "SMTP_USER", "SMTP_PASS"];
    const missingVariables = [];
    requiredVars.forEach((varName) => {
        if (!process.env[varName]) {
            missingVariables.push(varName);
        }
    });
    return {
        configured: missingVariables.length === 0,
        missingVariables,
    };
}
//# sourceMappingURL=email-service.js.map