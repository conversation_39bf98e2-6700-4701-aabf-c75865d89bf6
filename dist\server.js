#!/usr/bin/env node
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import dotenv from 'dotenv';
// Import our services
import { getAnswerFromResume, isValidQuestion, getSuggestedQuestions } from './cv-chat-service.js';
import { sendEmail, testEmailConfiguration, getEmailConfigurationStatus } from './email-service.js';
import { resumeData, getResumeAsText } from './resume-data.js';
// Load environment variables
dotenv.config();
// Create MCP server
const server = new McpServer({
    name: 'cv-chat-mcp-server',
    version: '1.0.0'
});
// Tool: Ask questions about the CV/Resume
server.registerTool('ask-cv-question', {
    title: 'Ask CV Question',
    description: 'Ask questions about the resume/CV and get AI-powered answers',
    inputSchema: {
        question: z.string().describe('The question to ask about the resume')
    }
}, async ({ question }) => {
    try {
        // Validate the question
        if (!isValidQuestion(question)) {
            return {
                content: [{
                        type: 'text',
                        text: 'Invalid question. Please provide a valid question (1-1000 characters).'
                    }],
                isError: true
            };
        }
        // Get answer from OpenRouter AI
        const response = await getAnswerFromResume(question);
        if (response.success) {
            return {
                content: [{
                        type: 'text',
                        text: response.answer
                    }]
            };
        }
        else {
            return {
                content: [{
                        type: 'text',
                        text: `Error: ${response.error || 'Failed to get answer'}`
                    }],
                isError: true
            };
        }
    }
    catch (error) {
        return {
            content: [{
                    type: 'text',
                    text: `Unexpected error: ${error.message}`
                }],
            isError: true
        };
    }
});
// Tool: Send email notifications
server.registerTool('send-email', {
    title: 'Send Email',
    description: 'Send email notifications via SMTP',
    inputSchema: {
        recipient: z.string().email().describe('Email address of the recipient'),
        subject: z.string().min(1).max(200).describe('Email subject line'),
        body: z.string().min(1).max(10000).describe('Email body content')
    }
}, async ({ recipient, subject, body }) => {
    try {
        // Check email configuration
        const configStatus = getEmailConfigurationStatus();
        if (!configStatus.configured) {
            return {
                content: [{
                        type: 'text',
                        text: `Email not configured. Missing environment variables: ${configStatus.missingVariables.join(', ')}`
                    }],
                isError: true
            };
        }
        // Send email
        const response = await sendEmail({ recipient, subject, body });
        if (response.success) {
            return {
                content: [{
                        type: 'text',
                        text: `Email sent successfully to ${recipient}. Message ID: ${response.messageId}`
                    }]
            };
        }
        else {
            return {
                content: [{
                        type: 'text',
                        text: `Failed to send email: ${response.error}`
                    }],
                isError: true
            };
        }
    }
    catch (error) {
        return {
            content: [{
                    type: 'text',
                    text: `Unexpected error: ${error.message}`
                }],
            isError: true
        };
    }
});
// Tool: Get suggested CV questions
server.registerTool('get-cv-suggestions', {
    title: 'Get CV Question Suggestions',
    description: 'Get a list of suggested questions you can ask about the resume',
    inputSchema: {}
}, async () => {
    try {
        const suggestions = getSuggestedQuestions();
        return {
            content: [{
                    type: 'text',
                    text: `Here are some suggested questions you can ask about the resume:\n\n${suggestions.map((q, i) => `${i + 1}. ${q}`).join('\n')}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: 'text',
                    text: `Error getting suggestions: ${error.message}`
                }],
            isError: true
        };
    }
});
// Tool: Test email configuration
server.registerTool('test-email-config', {
    title: 'Test Email Configuration',
    description: 'Test the email SMTP configuration',
    inputSchema: {}
}, async () => {
    try {
        const configStatus = getEmailConfigurationStatus();
        if (!configStatus.configured) {
            return {
                content: [{
                        type: 'text',
                        text: `Email configuration incomplete. Missing variables: ${configStatus.missingVariables.join(', ')}\n\nRequired environment variables:\n- SMTP_HOST\n- SMTP_PORT\n- SMTP_USER\n- SMTP_PASS`
                    }],
                isError: true
            };
        }
        const testResult = await testEmailConfiguration();
        if (testResult.success) {
            return {
                content: [{
                        type: 'text',
                        text: 'Email configuration test successful! SMTP connection is working.'
                    }]
            };
        }
        else {
            return {
                content: [{
                        type: 'text',
                        text: `Email configuration test failed: ${testResult.error}`
                    }],
                isError: true
            };
        }
    }
    catch (error) {
        return {
            content: [{
                    type: 'text',
                    text: `Error testing email configuration: ${error.message}`
                }],
            isError: true
        };
    }
});
// Resource: Resume data
server.registerResource('resume', 'resume://data', {
    title: 'Resume Data',
    description: 'Complete resume/CV data in text format',
    mimeType: 'text/plain'
}, async (uri) => {
    try {
        const resumeText = getResumeAsText();
        return {
            contents: [{
                    uri: uri.href,
                    text: resumeText,
                    mimeType: 'text/plain'
                }]
        };
    }
    catch (error) {
        throw new Error(`Failed to get resume data: ${error.message}`);
    }
});
// Resource: Resume JSON
server.registerResource('resume-json', 'resume://json', {
    title: 'Resume JSON Data',
    description: 'Complete resume/CV data in JSON format',
    mimeType: 'application/json'
}, async (uri) => {
    try {
        return {
            contents: [{
                    uri: uri.href,
                    text: JSON.stringify(resumeData, null, 2),
                    mimeType: 'application/json'
                }]
        };
    }
    catch (error) {
        throw new Error(`Failed to get resume JSON data: ${error.message}`);
    }
});
// Main function to start the server
async function main() {
    try {
        // Check for required environment variables
        if (!process.env.OPENROUTER_API_KEY) {
            console.error('Error: OPENROUTER_API_KEY environment variable is required');
            process.exit(1);
        }
        // Create stdio transport
        const transport = new StdioServerTransport();
        // Connect server to transport
        await server.connect(transport);
        console.error('CV Chat MCP Server is running...');
    }
    catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}
// Handle process termination
process.on('SIGINT', async () => {
    console.error('Shutting down server...');
    process.exit(0);
});
process.on('SIGTERM', async () => {
    console.error('Shutting down server...');
    process.exit(0);
});
// Start the server
main().catch((error) => {
    console.error('Server error:', error);
    process.exit(1);
});
//# sourceMappingURL=server.js.map